# 🌟 Naroop - Narrative of Our People

A beautiful, feature-rich social media platform where Black people come together to share positive stories, experiences, and celebrate community. Built with modern web technologies and AI integration to amplify Black voices and foster meaningful connections.

![Naroop Platform](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)
![Version](https://img.shields.io/badge/Version-4.0-blue)
![License](https://img.shields.io/badge/License-MIT-yellow)

## 🎯 Our Mission

**Naroop** (Narrative of Our People) is more than just a social platform—it's a digital sanctuary where Black voices are amplified, positive stories are celebrated, and our community thrives. We believe every experience, every triumph, and every moment of joy deserves to be shared and honored.

### 🌟 Core Values
- **Community First**: Building connections that uplift and inspire our community
- **Positive Stories**: Focusing on experiences that inspire, motivate, and celebrate
- **Safe Space**: A protected environment where our voices are heard and respected

## ✨ Features

### 🔐 User Management
- **User Registration & Authentication** - Secure signup and login system
- **Profile Management** - Editable profiles with bio and avatar
- **Follow System** - Follow/unfollow other users
- **User Discovery** - "People You May Know" suggestions

### 📝 Positive Storytelling
- **Rich Story Creation** - Share inspiring experiences with titles, content, and images
- **Image Uploads** - Add images to celebrate your moments with preview functionality
- **Story Categories** - 10 different categories for organizing positive experiences
- **AI-Powered Suggestions** - AI-generated titles, bios, and uplifting comments

### 💬 Community Engagement
- **Like System** - Show appreciation for inspiring stories with visual feedback
- **Comment System** - Engage with uplifting content and AI-generated positive suggestions
- **Share Functionality** - Amplify positive stories with counter tracking
- **Real-time Notifications** - Stay connected with community interactions
- **Direct Messaging** - Build meaningful connections through private messaging

### 🔍 Community Discovery
- **Advanced Search** - Find community members and inspiring stories with real-time results
- **Personalized Feed** - Smart algorithm highlighting positive content and community connections
- **Mobile Search** - Dedicated mobile search experience for discovering uplifting content
- **Story Discovery** - Find trending positive stories and inspiring community members

### 🛡️ Safety & Moderation
- **Content Moderation** - Automated filtering of inappropriate content
- **Spam Prevention** - Protection against spam and abuse
- **User Safety** - Community guidelines enforcement

### 📱 Mobile Experience
- **Responsive Design** - Optimized for all device sizes
- **Mobile-First UI** - Touch-friendly interactions
- **Progressive Enhancement** - Works on all modern browsers

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
```bash
git clone https://bitbucket.org/blackinsurance89/naroop.git
cd naroop
```

2. **Install dependencies**
```bash
npm install
```

3. **Start the server**
```bash
npm start
```

4. **Open your browser**
Navigate to `http://localhost:3000` and join the Naroop community!

## 🏗️ Architecture

### Backend (Node.js + Express)
- **RESTful API** - 20+ endpoints for all functionality
- **File-based Storage** - JSON files for data persistence
- **Content Moderation** - Automated content filtering
- **Real-time Features** - Notifications and messaging

### Frontend (Vanilla JavaScript + Tailwind CSS)
- **Modern UI** - Beautiful, responsive design
- **Real-time Updates** - Dynamic content loading
- **Mobile Optimized** - Touch-friendly interface
- **AI Integration** - Gemini AI for content suggestions

## 📡 API Endpoints

### User Management
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `PUT /api/users/:id` - Update user profile
- `GET /api/users` - Get all users
- `POST /api/users/:id/follow` - Follow/unfollow user

### Posts & Content
- `GET /api/posts` - Get all posts
- `GET /api/feed/:userId` - Get personalized feed
- `POST /api/posts` - Create new post
- `POST /api/posts/:id/like` - Like/unlike post
- `POST /api/posts/:id/comment` - Add comment
- `POST /api/posts/:id/share` - Share post

### Messaging & Notifications
- `GET /api/notifications/:userId` - Get user notifications
- `POST /api/messages` - Send message
- `GET /api/conversations/:userId` - Get user conversations
- `GET /api/messages/:userId1/:userId2` - Get conversation messages

### Search & Discovery
- `GET /api/search` - Search users and posts

## 🎨 UI Components

- **Header Navigation** - Naroop logo, search, notifications, user menu
- **Story Creation** - Rich text editor for sharing positive experiences
- **Community Feed** - Infinite scroll showcasing inspiring stories
- **Comment Modal** - Full-screen interface for community engagement
- **Messaging Interface** - Real-time chat system for building connections
- **Notification Center** - Stay updated with community interactions
- **Search Interface** - Discover community members and uplifting content

## 🤖 AI Integration

Powered by Google's Gemini AI for:
- **Title Suggestions** - AI-generated titles for inspiring stories
- **Bio Generation** - Personalized community member bios
- **Comment Assistance** - Smart suggestions for positive engagement
- **Content Enhancement** - Writing assistance for uplifting narratives

## 🔧 Configuration

### Environment Variables
```bash
PORT=3000                    # Server port (default: 3000)
GEMINI_API_KEY=your_key     # Optional: For AI features
```

### Data Storage
The application uses JSON files for data storage:
- `users.json` - User accounts and profiles
- `posts.json` - All posts and interactions
- `notifications.json` - User notifications
- `messages.json` - Direct messages

## 🚀 Deployment

### Local Development
```bash
npm start
```

### Production Deployment
1. Set environment variables
2. Ensure Node.js is installed on server
3. Run `npm install --production`
4. Start with `npm start`

### Recommended Hosting
- **Heroku** - Easy deployment with git integration
- **DigitalOcean** - VPS hosting for full control
- **Vercel** - Serverless deployment option

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**John D** - *Full Stack Developer*
- Bitbucket: [@blackinsurance89](https://bitbucket.org/blackinsurance89)

## 🙏 Acknowledgments

- **Tailwind CSS** - For beautiful, responsive styling
- **Lucide Icons** - For clean, modern iconography
- **Google Gemini AI** - For intelligent content suggestions
- **Express.js** - For robust backend framework

---

## 🚀 Repository Information

**Repository URL:** https://bitbucket.org/blackinsurance89/naroop
**Clone Command:** `git clone https://<EMAIL>/blackinsurance89/naroop.git`

## 📊 Development Phases Completed

- ✅ **Phase 1:** Core Branding Update - Updated to Naroop with community-focused messaging
- ✅ **Phase 2:** Enhanced Messaging & Content - Added mission statement and positive storytelling focus
- ✅ **Phase 3:** Documentation & Repository Updates - Complete rebranding and documentation
- ✅ **Phase 4:** Production-Ready Features - Full platform functionality

## 🎯 Current Status

**Status:** Production Ready ✅
**Version:** 4.0
**Last Updated:** January 2025
**Total Development Time:** 4 Complete Phases

## 🚀 Future Enhancements

### Recommended Improvements for Better Community Service

1. **Enhanced Community Features**
   - Community groups/circles for specific interests (e.g., entrepreneurship, arts, education)
   - Mentorship matching system connecting experienced community members with newcomers
   - Local community events and meetup integration

2. **Content & Storytelling Improvements**
   - Story templates for different types of positive experiences (achievements, overcoming challenges, community impact)
   - Featured story of the week/month highlighting exceptional community contributions
   - Audio storytelling support for oral tradition and accessibility

3. **Safety & Moderation Enhancements**
   - Community moderators from within the Black community
   - Reporting system specifically designed for community safety
   - Positive reinforcement system rewarding uplifting content

4. **Cultural Celebration Features**
   - Black History Month special features and story collections
   - Cultural event calendar integration
   - Heritage and ancestry sharing features

5. **Professional & Educational Support**
   - Career success story sharing
   - Educational achievement celebrations
   - Business and entrepreneurship showcase

---

**Built with ❤️ for the Black community - Celebrating our narratives, amplifying our voices**
